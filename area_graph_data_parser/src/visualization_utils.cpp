#include "visualization_utils.h"


using namespace osm_ag;

/**
*@brief Plot all nodes(points) in an image to check if the coordinate is correct.
*@param resolution The default is 0.1, is meter per pixel (m/pixel)
*/
void PlotNodePoints(AreaGraph& graph, cv::Mat& image, double resolution){
    // image的尺寸宏定义，需要用来算分辨率,需要根据规模进行缩放 TODO
    for(auto node_it = graph.nodes_.begin(); node_it!=graph.nodes_.end(); node_it++){
        int x = node_it->second->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y = node_it->second->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_2d(x,y);
        cv::circle(image, points_2d, 2, cv::Scalar(255,0,0)); // blue
    }
    cv::imwrite("/home/<USER>/lichq_ws/osmag_nav/src/osmag/data_parse/data/show_osmAG_nodes.png",image);
}

void PlotAreas(AreaGraph& graph, cv::Mat& image, double resolution){



    for(auto area_it = graph.areas_.begin(); area_it!=graph.areas_.end(); area_it++){
        cv::Point2i point_last;
        bool init = false;
        for(auto node_id : area_it->second->nodes_inorder_){
            int x = graph.nodes_[node_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
            int y = graph.nodes_[node_id]->attributes_->position[1]/resolution + IMAGE_H/4;
            cv::Point2i point(x,y);
            if(init){
                if(area_it->second->attributes_->tags["level"] == "1"){ //level 1 is green
                    cv::line(image, point_last, point, cv::Scalar(0,255,0)); // green
                }
                else{
                    cv::line(image, point_last, point, cv::Scalar(255, 191, 0)); // orange
                }
            }
            else{
                init = true; 
            }
            point_last = point;
        }
        // show the center of the area
        // AreaId area_id = area_it->first;
        // ComputeCenter_Area(graph, area_id);
        // int cenx = graph.areas_[area_id]->center_position[0]/resolution + 9 * IMAGE_W/10;
        // int ceny = graph.areas_[area_id]->center_position[1]/resolution + IMAGE_H/4;
        // cv::Point2i center(cenx,ceny) ;
        // cv::circle(image, center, 2, cv::Scalar(255,0,0)); // blue
    }
    cv::imwrite("/home/<USER>/lichq_ws/osmag_nav/src/osmag/data_parse/data/show_osmAG_areas.png",image);
}

void PlotPassages(AreaGraph& graph, cv::Mat& image, double resolution){
    for(auto passage_it = graph.passages_.begin(); passage_it != graph.passages_.end(); passage_it++){
        auto source_id = passage_it->second->passage_nodes.source;
        auto target_id = passage_it->second->passage_nodes.target;
        int x_s = graph.nodes_[source_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y_s = graph.nodes_[source_id]->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_s(x_s,y_s);

        int x_t = graph.nodes_[target_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y_t = graph.nodes_[target_id]->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_t(x_t,y_t);

        cv::line(image, points_s, points_t, cv::Scalar(0,0,255), 1.5); // red
    }
    cv::imwrite("/home/<USER>/lichq_ws/osmag_nav/src/osmag/data_parse/data/show_osmAG_passages.png",image);
}


void PlotPath(std::vector<Eigen::Vector3d> path, cv::Mat& image, double resolution, bool areapath){
    cv::Point2i points_2d_last;
    int i = 0;
    for(auto pt:path){
        int x = pt[0]/resolution + 9 * IMAGE_W/10;
        int y = pt[1]/resolution + IMAGE_H/4;
        cv::Point2i points_2d(x,y);
        if(i>0){
            // cv::line(image, points_2d_last, points_2d, cv::Scalar(0,0,0));
            if(areapath){cv::line(image, points_2d_last, points_2d, cv::Scalar(0,255,0),2);} // green
            else{cv::line(image, points_2d_last, points_2d, cv::Scalar(255,0,0),2);} // blue
        }else{
            // cv::circle(image, points_2d, 3, cv::Scalar(100,100,50));
        }
        points_2d_last = points_2d;
        i++;
    }
}

void PlotAreaInGrid(AreaGraph& graph, cv::Mat& image, AreaId id, double resolution){
    std::vector<std::vector<cv::Point2i>> contour_pts(1,std::vector<cv::Point2i> ());
    cv::Point2i points_2d_last;
    int i = 0;
    // draw the contours
    for(auto node_id : graph.areas_[id]->nodes_inorder_){
        int x = graph.nodes_[node_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y = graph.nodes_[node_id]->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_2d(x,y);
        if(i>0){
            contour_pts[0].push_back(points_2d);
            cv::line(image, points_2d_last, points_2d, cv::Scalar(0,0,0));
        }
        points_2d_last = points_2d;
        i++;
    }
    // draw the white region as free area
    cv::drawContours(image,contour_pts,-1,cv::Scalar(255,255,255),cv::FILLED);//空闲区域是白色的

    // draw area contours boundary
    i=0;
    NodeId node_id_last;
    for(auto node_id : graph.areas_[id]->nodes_inorder_){
        int x = graph.nodes_[node_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y = graph.nodes_[node_id]->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_2d(x,y);
        if(i>0){
            std::pair<NodeId,NodeId> p(node_id,node_id_last);
            std::pair<NodeId,NodeId> p_i(node_id_last,node_id);
            if( !graph.areas_[id]->passage_nodes.count(p) && !graph.areas_[id]->passage_nodes.count(p_i)){ // not passage
                cv::line(image, points_2d_last, points_2d, cv::Scalar(0), 1);
            }
        }
        points_2d_last = points_2d;  // update point
        node_id_last = node_id;  // update nodeid
        i++;
    }
    // cv::imshow("1", image);
    // cv::waitKey(0);
}

void PlotHighAreaInGrid(AreaGraph& graph, cv::Mat& image, AreaId id, double resolution){
    std::vector<std::vector<cv::Point2i>> contour_pts(1,std::vector<cv::Point2i> ());
    cv::Point2i points_2d_last;
    int i = 0;
    NodeId node_id_last;
    for(auto node_id : graph.areas_[id]->nodes_inorder_){
        int x = graph.nodes_[node_id]->attributes_->position[0]/resolution + 9 * IMAGE_W/10;
        int y = graph.nodes_[node_id]->attributes_->position[1]/resolution + IMAGE_H/4;
        cv::Point2i points_2d(x,y);
        if(i>0){
            std::pair<NodeId,NodeId> p(node_id,node_id_last);
            std::pair<NodeId,NodeId> p_i(node_id_last,node_id);
            if(!graph.areas_[id]->passage_nodes.count(p) && !graph.areas_[id]->passage_nodes.count(p_i)){ //not belong the passage
                cv::line(image, points_2d_last, points_2d, cv::Scalar(0), 1);
            }
        }
        points_2d_last = points_2d;
        node_id_last = node_id;
        i++;
    }
}

void PlotAreasInGrid(AreaGraph& graph, cv::Mat& image1, cv::Mat& image2, double resolution){

    for(auto area_it = graph.areas_.begin(); area_it!=graph.areas_.end(); area_it++){
        if(area_it->second->attributes_->tags["level"] == "1" ){
            if(area_it->second->has_parent_){//前提：在pathgraph那里进行叶子的判断！！！需要改进
                PlotAreaInGrid(graph, image1, area_it->first, resolution);
            }
            else{
                PlotHighAreaInGrid(graph, image1, area_it->first, resolution);
            }
        }
        else{
            if(area_it->second->has_parent_){//前提：在pathgraph那里进行叶子的判断！！！需要改进
                PlotAreaInGrid(graph, image2, area_it->first, resolution);
            }
            else{
                PlotHighAreaInGrid(graph, image2, area_it->first, resolution);
            }
        }
    }
}

cv::Point2i XYZ2Grid(Eigen::Vector3d point, double resolution){
    int x_grid = point[0]/resolution + 9 * IMAGE_W/10;
    int y_grid = point[1]/resolution +  IMAGE_H/4;
    cv::Point2i result(x_grid,y_grid);
    return result;
}

Eigen::Vector3d Grid2XYZ_vis(cv::Point pt_grid, double resolution){
    Eigen::Vector3d result;
    result[0] = (pt_grid.x - 9 * IMAGE_W/10) * resolution;
    result[1] = (pt_grid.y - IMAGE_H/4) * resolution;
    result[2] = 0;
    return result;
}

void plotAndSaveImages(AreaGraph& graph) {
    cv::Mat img(cv::Size(IMAGE_W, IMAGE_H), CV_8UC3, cv::Scalar(255, 255, 255));

    // TODO 这三个函数在定义中都写死了绝对路径，我希望改进，将路径通过参数传递
    PlotNodePoints(graph, img);
    PlotAreas(graph, img);
    PlotPassages(graph, img);

    cv::Mat gray_img;
    cv::cvtColor(img, gray_img, cv::COLOR_BGR2GRAY);
    cv::imwrite("/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/show_map.pgm", gray_img);

    cv::Mat gridmap_1(cv::Size(IMAGE_W, IMAGE_H), CV_8UC1, cv::Scalar(125));
    cv::Mat gridmap_2(cv::Size(IMAGE_W, IMAGE_H), CV_8UC1, cv::Scalar(125));

    PlotAreasInGrid(graph, gridmap_1, gridmap_2, 0.1);

    if (gridmap_1.type() == CV_8UC1) {
        cv::cvtColor(gridmap_1, gridmap_1, cv::COLOR_GRAY2BGR);
        cv::cvtColor(gridmap_2, gridmap_2, cv::COLOR_GRAY2BGR);
    }

    cv::Mat grayImage1, grayImage2;
    cv::cvtColor(gridmap_1, grayImage1, cv::COLOR_BGR2GRAY);
    cv::flip(grayImage1, grayImage1, 0);
    cv::imwrite("/home/<USER>/agilex_ws/src/area_graph_data_parser/data/show_grid1.pgm", grayImage1);

    cv::cvtColor(gridmap_2, grayImage2, cv::COLOR_BGR2GRAY);
    cv::flip(grayImage2, grayImage2, 0);
    cv::imwrite("/home/<USER>/agilex_ws/src/area_graph_data_parser/data/show_grid2.pgm", grayImage2);
}


// 新增：LevelVisualization的getMarkerArray实现
visualization_msgs::msg::MarkerArray LevelVisualization::getMarkerArray() const {
    visualization_msgs::msg::MarkerArray markers;
    int marker_id = 0;
    
    // 遍历所有结构
    for(const auto& [structure_id, structure] : structures) {
        // 添加结构轮廓
        auto structure_marker = structure.structure_outline;
        structure_marker.id = marker_id++;
        markers.markers.push_back(structure_marker);
        
        // 添加房间标记
        for(const auto& room : structure.rooms) {
            auto room_marker = room;
            room_marker.id = marker_id++;
            markers.markers.push_back(room_marker);
        }
        
        // 添加通道标记
        for(const auto& passage : structure.passages) {
            auto passage_marker = passage;
            passage_marker.id = marker_id++;
            markers.markers.push_back(passage_marker);
        }
    }
    
    return markers;
}

// 新增：颜色生成辅助函数实现
std_msgs::msg::ColorRGBA createColor(float r, float g, float b, float a) {
    std_msgs::msg::ColorRGBA color;
    color.r = r;
    color.g = g;
    color.b = b;
    color.a = a;
    return color;
}

std_msgs::msg::ColorRGBA generateLevelColor(int level, int total_levels) {
    // 使用HSV色彩空间生成均匀分布的颜色
    float hue = static_cast<float>(level) / total_levels;
    float saturation = 0.7f;
    float value = 0.9f;
    
    // HSV转RGB
    float c = value * saturation;
    float x = c * (1 - std::abs(std::fmod(hue * 6, 2) - 1));
    float m = value - c;
    
    float r = 0, g = 0, b = 0;
    if(hue < 1.0f/6.0f) {
        r = c; g = x; b = 0;
    } else if(hue < 2.0f/6.0f) {
        r = x; g = c; b = 0;
    } else if(hue < 3.0f/6.0f) {
        r = 0; g = c; b = x;
    } else if(hue < 4.0f/6.0f) {
        r = 0; g = x; b = c;
    } else if(hue < 5.0f/6.0f) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    return createColor(r + m, g + m, b + m, 0.7f);
}


void createVisualizationData(
    const osm_ag::AreaGraph& graph,
    std::map<int, LevelVisualization>& level_visuals,
    const LevelConfig* config)
{
    // 清理旧数据
    level_visuals.clear();

    std::cout << "=== Creating Visualization Data ===" << std::endl;
    std::cout << "Total areas: " << graph.areas_.size() << std::endl;
    std::cout << "Total passages: " << graph.passages_.size() << std::endl;

    // 获取所有层级
    std::set<int> all_levels;
    for(const auto& [area_id, area] : graph.areas_) {
        if(!area->attributes_->tags["level"].empty()) {
            all_levels.insert(std::stoi(area->attributes_->tags["level"]));
        }
    }
    int total_levels = all_levels.size();

    std::cout << "Found levels: ";
    for(int level : all_levels) {
        std::cout << level << " ";
    }
    std::cout << std::endl;

    // 初始化每层可视化数据
    for(int level : all_levels) {
        level_visuals[level].level = level;
        level_visuals[level].name = "Level " + std::to_string(level);
        // 特别处理：level 0的显示高度与level 1相同
        if(level == 0) {
            level_visuals[level].height = config ? config->height_per_level : 8.0;
        } else {
            level_visuals[level].height = config ? level * config->height_per_level : level * 8.0;
        }
    }

    // 建立structure-room映射
    std::map<osm_ag::AreaId, std::set<osm_ag::AreaId>> structure_rooms;
    for(const auto& [area_id, area] : graph.areas_) {
        if(area->has_parent_) {
            structure_rooms[area->parent_].insert(area_id);
        }
    }

    // 处理所有区域
    for(const auto& [area_id, area] : graph.areas_) {
        if(area->type != "structure" || area->attributes_->tags["level"].empty()) continue;

        int level = std::stoi(area->attributes_->tags["level"]);
        std::string structure_name = area->attributes_->tags["name"];
        auto& level_visual = level_visuals[level];
        auto& structure_markers = level_visual.structures[area_id];
        
        // 设置结构名称
        structure_markers.name = structure_name;

        // 所有marker使用同一个namespace！
        std::string common_ns = "level_" + std::to_string(level) + "/" + structure_name;

        // 创建结构轮廓marker
        auto& outline = structure_markers.structure_outline;
        outline.header.frame_id = "AGmap";
        outline.type = visualization_msgs::msg::Marker::LINE_STRIP;
        outline.action = visualization_msgs::msg::Marker::ADD;
        outline.scale.x = 0.3;
        outline.color = createColor(0.8, 0.8, 0.2, 0.9);
        outline.pose.orientation.w = 1.0;
        outline.ns = common_ns;  // 使用共同的namespace

        // 添加结构轮廓点
        for(const auto& node_id : area->nodes_inorder_) {
            geometry_msgs::msg::Point p;
            const auto& node = graph.nodes_.at(node_id);
            p.x = node->attributes_->position[0];
            p.y = node->attributes_->position[1];
            p.z = level_visual.height;
            outline.points.push_back(p);
        }

        // 处理该结构的所有房间
        if(structure_rooms.count(area_id)) {
            for(osm_ag::AreaId room_id : structure_rooms.at(area_id)) {
                const auto& room = graph.areas_.at(room_id);
                
                visualization_msgs::msg::Marker room_marker;
                room_marker.header.frame_id = "AGmap";
                room_marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
                room_marker.action = visualization_msgs::msg::Marker::ADD;
                room_marker.scale.x = 0.15;
                room_marker.pose.orientation.w = 1.0;
                room_marker.ns = common_ns;  // 使用共同的namespace
                
                // 设置房间颜色
                if(config && config->level_colors.count(level)) {
                    room_marker.color = config->level_colors.at(level);
                } else {
                    room_marker.color = generateLevelColor(level, total_levels);
                }

                // 添加房间轮廓点
                for(const auto& node_id : room->nodes_inorder_) {
                    geometry_msgs::msg::Point p;
                    const auto& node = graph.nodes_.at(node_id);
                    p.x = node->attributes_->position[0];
                    p.y = node->attributes_->position[1];
                    p.z = level_visual.height;
                    room_marker.points.push_back(p);
                }

                structure_markers.rooms.push_back(room_marker);

                // 处理房间的通道
                for(const auto& passage_id : room->passageids) {
                    const auto& passage = graph.passages_.at(passage_id);
                    
                    // 检查是否为当前层级的通道或跨楼层垂直连接通道
                    bool is_current_level = passage->info->tags["level"] == std::to_string(level);
                    bool is_vertical_passage = false;
                    int target_level = -1;
                    
                    // 检查是否为跨楼层垂直连接通道（电梯或楼梯）
                    // 使用area_from和area_to来判断，而不是已删除的osmAG标签
                    if (graph.areas_.count(passage->area_from) && graph.areas_.count(passage->area_to)) {
                        const auto& from_area = graph.areas_.at(passage->area_from);
                        const auto& to_area = graph.areas_.at(passage->area_to);

                        // 检查两个区域是否在不同楼层但有相同名称（跨楼层连接）
                        if (from_area->attributes_->tags.count("level") &&
                            to_area->attributes_->tags.count("level") &&
                            from_area->attributes_->tags.count("name") &&
                            to_area->attributes_->tags.count("name")) {

                            int from_level = std::stoi(from_area->attributes_->tags["level"]);
                            int to_level = std::stoi(to_area->attributes_->tags["level"]);
                            std::string from_name = from_area->attributes_->tags["name"];
                            std::string to_name = to_area->attributes_->tags["name"];

                            // 跨楼层垂直连接：不同楼层但同名区域（如电梯、楼梯）
                            if (from_level != to_level && from_name == to_name) {
                                is_vertical_passage = true;
                                // 确定目标层级（非当前层级的那个）
                                target_level = (from_level == level) ? to_level : from_level;

                                // 调试日志
                                std::cout << "Found vertical passage " << passage_id
                                         << ": " << from_name << " (L" << from_level
                                         << " -> L" << to_level << ")" << std::endl;
                            }
                        }
                    }
                    
                    // 处理同层通道或垂直连接通道
                    if (is_current_level || is_vertical_passage) {
                        visualization_msgs::msg::Marker passage_marker;
                        passage_marker.header.frame_id = "AGmap";
                        passage_marker.type = visualization_msgs::msg::Marker::LINE_LIST;
                        passage_marker.action = visualization_msgs::msg::Marker::ADD;
                        passage_marker.scale.x = 0.2;
                        passage_marker.pose.orientation.w = 1.0;
                        passage_marker.ns = common_ns;  // 使用共同的namespace
                        
                        // 为垂直连接通道使用特殊颜色（蓝色）
                        if (is_vertical_passage) {
                            passage_marker.color = createColor(1.0, 0.0, 1.0, 0.9); // 蓝色
                            passage_marker.scale.x = 0.3; // 稍微粗一点
                            std::cout << "Creating vertical passage marker with target_level: "
                                     << target_level << std::endl;
                        } else {
                            passage_marker.color = createColor(1.0, 0.0, 0.0, 0.7); // 红色
                        }

                        // 添加通道端点
                        geometry_msgs::msg::Point p1, p2;
                        const auto& node1 = graph.nodes_.at(passage->passage_nodes.source);
                        const auto& node2 = graph.nodes_.at(passage->passage_nodes.target);
                        
                        p1.x = node1->attributes_->position[0];
                        p1.y = node1->attributes_->position[1];
                        p1.z = level_visual.height;
                        
                        p2.x = node2->attributes_->position[0];
                        p2.y = node2->attributes_->position[1];
                        
                        // 对于垂直连接通道，设置目标点的z坐标为目标层级的高度
                        if (is_vertical_passage && target_level >= 0) {
                            // 查找目标层级的高度
                            double target_height = 0.0;
                            if (level_visuals.count(target_level)) {
                                target_height = level_visuals[target_level].height;
                            } else {
                                // 如果目标层级未找到，使用配置的高度计算
                                target_height = config ? target_level * config->height_per_level : target_level * 8.0;
                                // 特别处理：level 0的显示高度与level 1相同
                                if (target_level == 0) {
                                    target_height = config ? config->height_per_level : 8.0;
                                }
                            }
                            p2.z = target_height;
                        } else {
                            p2.z = level_visual.height;
                        }
                        
                        passage_marker.points.push_back(p1);
                        passage_marker.points.push_back(p2);

                        structure_markers.passages.push_back(passage_marker);
                    }
                    
                }
            }
        }
    }
}